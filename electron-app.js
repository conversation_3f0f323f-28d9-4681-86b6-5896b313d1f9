// Electron版本的TVBox应用逻辑
class ElectronTVBox {
    constructor() {
        this.currentDataSource = null;
        this.videoSites = [];
        this.searchResults = [];
        this.baseUrl = 'http://localhost:3001';
        
        this.init();
    }

    init() {
        console.log('TVBox Desktop 初始化...');
        this.setupEventListeners();
        this.checkServerConnection();
    }

    setupEventListeners() {
        // 搜索框回车事件
        const searchInput = document.getElementById('searchKeyword');
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.searchVideos();
                }
            });
        }
    }

    async checkServerConnection() {
        try {
            const response = await fetch(`${this.baseUrl}/api/proxy?url=https://httpbin.org/json`);
            if (response.ok) {
                this.showStatus('sourceStatus', '✅ Electron服务器连接正常', 'success');
            }
        } catch (error) {
            this.showStatus('sourceStatus', '❌ Electron服务器连接失败', 'error');
        }
    }

    // 使用Electron的代理API
    async fetchWithProxy(url) {
        try {
            const response = await fetch(`${this.baseUrl}/api/proxy?url=${encodeURIComponent(url)}`);
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `HTTP ${response.status}`);
            }
            
            return response;
        } catch (error) {
            console.error('代理请求失败:', error);
            throw error;
        }
    }

    // 加载数据源
    async loadDataSource(url) {
        if (!url) {
            url = document.getElementById('sourceUrl').value.trim();
        }
        
        if (!url) {
            this.showStatus('sourceStatus', '请输入数据源URL', 'error');
            return;
        }

        this.showStatus('sourceStatus', '正在加载数据源...', 'loading');

        try {
            const response = await this.fetchWithProxy(url);
            const data = await response.json();

            this.currentDataSource = data;
            this.videoSites = data.sites || [];

            this.updateSiteSelector();
            this.displaySearchableSites();

            // 显示详细的数据源信息
            const dataInfo = `
                <div>
                    <strong>数据源加载成功！</strong><br>
                    找到 ${this.videoSites.length} 个视频站点<br>
                    <details style="margin-top: 10px;">
                        <summary>查看数据源结构</summary>
                        <pre style="background: #f5f5f5; padding: 10px; border-radius: 5px; font-size: 12px; max-height: 300px; overflow-y: auto;">
${JSON.stringify(data, null, 2)}
                        </pre>
                    </details>
                </div>
            `;

            this.showStatus('sourceStatus', dataInfo, 'success');

            console.log('数据源加载成功:', data);

        } catch (error) {
            console.error('加载数据源失败:', error);
            this.showStatus('sourceStatus', `❌ 加载失败: ${error.message}`, 'error');
        }
    }

    // 更新站点选择器
    updateSiteSelector() {
        const siteSelect = document.getElementById('siteSelect');
        if (!siteSelect) return;

        siteSelect.innerHTML = '<option value="">选择站点</option>';

        this.videoSites.forEach(site => {
            if (site.searchable !== false && site.api && site.api.startsWith('http')) {
                const option = document.createElement('option');
                option.value = site.key;
                option.textContent = `${site.name} (类型${site.type})`;
                siteSelect.appendChild(option);
            }
        });
    }

    // 显示可搜索的站点
    displaySearchableSites() {
        const httpApiSites = this.videoSites.filter(site => 
            site.api && 
            site.api.startsWith('http') && 
            !site.api.includes('csp_')
        );

        const cspSites = this.videoSites.filter(site => 
            site.api && (site.api.startsWith('csp_') || site.api.includes('csp_'))
        );

        const container = document.getElementById('searchResults');
        if (!container) return;

        const html = `
            <h3>🖥️ Electron版 - HTTP API站点 (${httpApiSites.length}/${this.videoSites.length})</h3>
            ${httpApiSites.length > 0 ? `
            <div class="video-grid">
                ${httpApiSites.slice(0, 20).map(site => `
                    <div class="video-card" onclick="electronTVBox.selectSiteForSearch('${site.key}')">
                        <div class="video-card-content">
                            <h3>${site.name}</h3>
                            <p><strong>类型:</strong> ${site.type}</p>
                            <p><strong>API:</strong> ${site.api.length > 50 ? site.api.substring(0, 50) + '...' : site.api}</p>
                            <p><strong>状态:</strong> <span style="color: green;">✅ Electron可用</span></p>
                            <button class="btn" onclick="event.stopPropagation(); electronTVBox.testSiteApi('${site.key}')" style="margin-top: 10px;">测试连接</button>
                        </div>
                    </div>
                `).join('')}
            </div>
            ` : '<p>没有找到HTTP API站点</p>'}
            
            <details style="margin-top: 20px;">
                <summary><strong>CSP协议站点 (${cspSites.length}个)</strong></summary>
                <p>🔧 CSP协议站点正在开发中，将在后续版本支持</p>
                <div style="max-height: 200px; overflow-y: auto; background: #f5f5f5; padding: 10px; border-radius: 5px;">
                    ${cspSites.slice(0, 10).map(site => `
                        <p>• ${site.name} (${site.api})</p>
                    `).join('')}
                    ${cspSites.length > 10 ? `<p>... 还有 ${cspSites.length - 10} 个</p>` : ''}
                </div>
            </details>
        `;

        container.innerHTML = html;
    }

    // 测试站点API
    async testSiteApi(siteKey) {
        const site = this.videoSites.find(s => s.key === siteKey);
        if (!site) return;

        try {
            const response = await fetch(`${this.baseUrl}/api/search?site=${encodeURIComponent(site.api)}&keyword=测试&type=${site.type}`);
            
            if (response.ok) {
                const data = await response.json();
                console.log(`${site.name} 测试成功:`, data);
                
                // 更新UI显示测试成功
                const button = event.target;
                button.textContent = '✅ 可用';
                button.style.backgroundColor = '#48bb78';
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            console.error(`测试站点 ${site.name} 失败:`, error);
            
            const button = event.target;
            button.textContent = '❌ 失败';
            button.style.backgroundColor = '#f56565';
        }
    }

    // 选择站点进行搜索
    selectSiteForSearch(siteKey) {
        const site = this.videoSites.find(s => s.key === siteKey);
        if (!site) return;

        const siteSelect = document.getElementById('siteSelect');
        if (siteSelect) {
            siteSelect.value = siteKey;
        }

        this.showStatus('sourceStatus', `已选择站点: ${site.name}`, 'success');
    }

    // 搜索视频
    async searchVideos() {
        const keyword = document.getElementById('searchKeyword').value.trim();
        const selectedSiteKey = document.getElementById('siteSelect').value;

        if (!keyword) {
            this.showStatus('sourceStatus', '请输入搜索关键词', 'error');
            return;
        }

        if (!selectedSiteKey) {
            this.showStatus('sourceStatus', '请选择一个站点', 'error');
            return;
        }

        const site = this.videoSites.find(s => s.key === selectedSiteKey);
        if (!site) {
            this.showStatus('sourceStatus', '站点不存在', 'error');
            return;
        }

        this.showLoading('searchResults', `正在 ${site.name} 中搜索 "${keyword}"...`);

        try {
            const response = await fetch(`${this.baseUrl}/api/search?site=${encodeURIComponent(site.api)}&keyword=${encodeURIComponent(keyword)}&type=${site.type}`);
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `HTTP ${response.status}`);
            }

            const data = await response.json();
            this.displaySearchResults(data, site);

        } catch (error) {
            console.error('搜索失败:', error);
            document.getElementById('searchResults').innerHTML = `
                <div class="error">
                    在 ${site.name} 搜索失败: ${error.message}
                </div>
            `;
        }
    }

    // 显示搜索结果
    displaySearchResults(data, site) {
        const container = document.getElementById('searchResults');

        if (!data || !data.list || data.list.length === 0) {
            container.innerHTML = `
                <div class="loading">
                    在 ${site.name} 中没有找到相关内容
                </div>
            `;
            return;
        }

        const videos = data.list;
        const html = `
            <h3>在 ${site.name} 找到 ${videos.length} 个结果</h3>
            <div class="video-grid">
                ${videos.map(video => `
                    <div class="video-card" onclick="electronTVBox.playVideo('${video.vod_play_url || video.vod_play_from}', '${video.vod_name}')">
                        <div class="video-card-content">
                            <h3>${video.vod_name}</h3>
                            <p><strong>类型:</strong> ${video.type_name || '未知'}</p>
                            <p><strong>年份:</strong> ${video.vod_year || '未知'}</p>
                            <p><strong>地区:</strong> ${video.vod_area || '未知'}</p>
                            <p><strong>简介:</strong> ${(video.vod_content || video.vod_blurb || '无简介').substring(0, 100)}...</p>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;

        container.innerHTML = html;
    }

    // 播放视频
    playVideo(playInfo, title) {
        const playerSection = document.getElementById('playerSection');
        const videoPlayer = document.getElementById('videoPlayer');
        const videoInfo = document.getElementById('videoInfo');

        playerSection.style.display = 'block';

        // 解析播放信息
        let videoUrl = '';
        if (typeof playInfo === 'string') {
            const lines = playInfo.split('\n').filter(line => line.trim());
            if (lines.length > 0) {
                const firstLine = lines[0];
                if (firstLine.includes('$')) {
                    videoUrl = firstLine.split('$')[1];
                } else {
                    videoUrl = firstLine;
                }
            }
        } else {
            videoUrl = playInfo;
        }

        videoInfo.innerHTML = `
            <h3>🖥️ Electron播放: ${title}</h3>
            <p>视频地址: ${videoUrl}</p>
        `;

        if (videoUrl && (videoUrl.startsWith('http') || videoUrl.endsWith('.m3u8') || videoUrl.endsWith('.mp4'))) {
            videoPlayer.style.display = 'block';
            videoPlayer.src = videoUrl;
        } else {
            videoPlayer.style.display = 'none';
            videoInfo.innerHTML += `
                <div class="error">
                    无法解析播放地址，可能需要进一步处理。
                </div>
            `;
        }

        playerSection.scrollIntoView({ behavior: 'smooth' });
    }

    // 工具方法
    showStatus(elementId, message, type = 'info') {
        const element = document.getElementById(elementId);
        if (!element) return;

        element.className = type;
        element.innerHTML = message;

        if (type === 'success') {
            setTimeout(() => {
                element.innerHTML = '';
                element.className = '';
            }, 3000);
        }
    }

    showLoading(elementId, message = '加载中...') {
        const element = document.getElementById(elementId);
        if (!element) return;

        element.innerHTML = `<div class="loading">${message}</div>`;
    }
}

// 创建全局实例
const electronTVBox = new ElectronTVBox();

// 导出到全局作用域供HTML调用
window.electronTVBox = electronTVBox;
window.loadPresetSource = (name, url) => {
    document.getElementById('sourceUrl').value = url;
    electronTVBox.showStatus('sourceStatus', `已选择: ${name}`, 'success');
    electronTVBox.loadDataSource(url);
};
window.loadDataSource = () => electronTVBox.loadDataSource();
window.searchVideos = () => electronTVBox.searchVideos();
