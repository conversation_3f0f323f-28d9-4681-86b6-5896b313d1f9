<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web TVBox - 网页版视频播放器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .config-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .config-section h2 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .input-group input, .input-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .preset-sources {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .preset-btn {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            color: #495057;
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s;
            font-size: 12px;
        }
        
        .preset-btn:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .search-section {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .results-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .video-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            border: 2px solid transparent;
        }
        
        .video-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-color: #667eea;
        }
        
        .video-card h3 {
            color: #333;
            margin-bottom: 8px;
            font-size: 1.1em;
        }
        
        .video-card p {
            color: #666;
            font-size: 0.9em;
            line-height: 1.4;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }
        
        .player-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .video-player {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .video-player video {
            width: 100%;
            height: auto;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .search-section {
                grid-template-columns: 1fr;
            }
            
            .video-grid {
                grid-template-columns: 1fr;
            }
            
            .preset-sources {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 Web TVBox</h1>
            <p>网页版视频播放器 - 支持TVBox数据源</p>
        </div>
        
        <div class="config-section">
            <h2>📡 数据源配置</h2>
            
            <div class="preset-sources">
                <div class="preset-btn" onclick="loadPresetSource('蓝鲸直播源', 'https://raw.githubusercontent.com/Cyril0563/lanjing_live/main/TVbox_Free/tv.txt')">蓝鲸直播源</div>
                <div class="preset-btn" onclick="loadPresetSource('小盒子单仓', 'http://52bsj.vip:81/api/v3/file/get/29899/box2.json?sign=3cVyKZQr3lFAwdBUhL7d1KFruIrv2UNnBxyA8NaI7c4%3D%3A0')">小盒子单仓</div>
                <div class="preset-btn" onclick="loadPresetSource('小盒子4K', 'http://52bsj.vip:81/api/v3/file/get/41063/box2.json?sign=3cVyKZQr3lFAwdBUhL7d1KFruIrv2UNnBxyA8NaI7c4%3D%3A0')">小盒子4K</div>
                <div class="preset-btn" onclick="loadPresetSource('肥猫接口', 'http://我不是.肥猫.love:63/接口禁止贩卖')">肥猫接口</div>
                <div class="preset-btn" onclick="loadPresetSource('饭太硬', 'http://饭太硬.top/tv')">饭太硬</div>
                <div class="preset-btn" onclick="loadPresetSource('摸鱼接口', 'https://raw.githubusercontent.com/2hacc/TVBox/main/tvbox.json')">摸鱼接口</div>
                <div class="preset-btn" onclick="loadPresetSource('OK线路', 'https://raw.githubusercontent.com/tv-player/tvbox-line/main/tv/fj.json')">OK线路</div>
            </div>
            
            <div class="input-group">
                <label for="sourceUrl">自定义数据源URL:</label>
                <input type="text" id="sourceUrl" placeholder="请输入TVBox数据源接口地址...">
            </div>
            
            <button class="btn" onclick="loadDataSource()">🔄 加载数据源</button>
            <button class="btn" onclick="testSource()">🧪 测试连接</button>
            
            <div id="sourceStatus"></div>
        </div>
        
        <div class="config-section">
            <h2>🔍 视频搜索</h2>
            
            <div class="search-section">
                <div class="input-group">
                    <input type="text" id="searchKeyword" placeholder="请输入要搜索的影片名称..." onkeypress="handleSearchKeyPress(event)">
                </div>
                <button class="btn" onclick="searchVideos()">🔍 搜索</button>
            </div>
            
            <div class="input-group">
                <label for="siteSelect">选择搜索站点:</label>
                <select id="siteSelect">
                    <option value="">所有站点</option>
                </select>
            </div>
        </div>
        
        <div class="results-section">
            <h2>📺 搜索结果</h2>
            <div id="searchResults">
                <div class="loading">请先配置数据源并搜索视频</div>
            </div>
        </div>
        
        <div class="player-section" id="playerSection" style="display: none;">
            <h2>🎥 视频播放</h2>
            <div class="video-player">
                <video id="videoPlayer" controls>
                    您的浏览器不支持视频播放
                </video>
            </div>
            <div id="videoInfo"></div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
