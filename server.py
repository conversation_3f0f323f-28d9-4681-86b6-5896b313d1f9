#!/usr/bin/env python3
"""
简单的HTTP服务器，用于本地测试Web TVBox播放器
支持CORS和代理功能
"""

import http.server
import socketserver
import urllib.request
import urllib.parse
import json
import os
import ssl
from urllib.error import URLError, HTTPError

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # 添加CORS头
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

    def do_OPTIONS(self):
        # 处理预检请求
        self.send_response(200)
        self.end_headers()

    def do_GET(self):
        # 处理代理请求
        if self.path.startswith('/proxy?url='):
            self.handle_proxy_request()
        else:
            # 处理静态文件
            super().do_GET()

    def handle_proxy_request(self):
        """处理代理请求，解决CORS问题"""
        try:
            # 解析URL参数
            url_param = self.path.split('url=', 1)[1]
            target_url = urllib.parse.unquote(url_param)

            print(f"代理请求: {target_url}")

            # 创建SSL上下文，忽略证书验证
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            # 创建请求
            req = urllib.request.Request(target_url)
            req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')

            # 发送请求
            with urllib.request.urlopen(req, timeout=10, context=ssl_context) as response:
                content = response.read()
                content_type = response.headers.get('Content-Type', 'application/json')

                # 发送响应
                self.send_response(200)
                self.send_header('Content-Type', content_type)
                self.end_headers()
                self.wfile.write(content)

        except HTTPError as e:
            error_msg = f"HTTP Error {e.code}: {e.reason}"
            self.send_error_safe(e.code, error_msg)
        except URLError as e:
            error_msg = f"Network Error: {str(e.reason)}"
            self.send_error_safe(500, error_msg)
        except Exception as e:
            error_msg = f"Server Error: {str(e)}"
            self.send_error_safe(500, error_msg)

    def send_error_safe(self, code, message):
        """安全地发送错误响应，避免Unicode编码问题"""
        try:
            # 确保消息是ASCII兼容的
            safe_message = message.encode('ascii', 'ignore').decode('ascii')
            self.send_response(code)
            self.send_header('Content-Type', 'application/json; charset=utf-8')
            self.end_headers()

            error_response = {
                'error': True,
                'code': code,
                'message': safe_message,
                'original_message': message
            }

            self.wfile.write(json.dumps(error_response, ensure_ascii=False).encode('utf-8'))
        except Exception as e:
            # 最后的备用方案
            self.send_response(500)
            self.send_header('Content-Type', 'text/plain')
            self.end_headers()
            self.wfile.write(b'Internal Server Error')

def main():
    PORT = 8000
    
    print("=" * 50)
    print("🎬 Web TVBox 本地服务器")
    print("=" * 50)
    print(f"服务器地址: http://localhost:{PORT}")
    print(f"代理接口: http://localhost:{PORT}/proxy?url=<目标URL>")
    print("=" * 50)
    print("使用说明:")
    print("1. 在浏览器中打开 http://localhost:8000")
    print("2. 配置数据源时，如果遇到CORS问题，")
    print("   可以使用代理接口: /proxy?url=<数据源地址>")
    print("3. 按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    # 检查文件是否存在
    if not os.path.exists('index.html'):
        print("❌ 错误: 找不到 index.html 文件")
        print("请确保在正确的目录中运行此脚本")
        return
    
    try:
        with socketserver.TCPServer(("", PORT), CORSHTTPRequestHandler) as httpd:
            print(f"✅ 服务器已启动在端口 {PORT}")
            print("正在等待连接...")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 错误: 端口 {PORT} 已被占用")
            print("请尝试关闭其他使用该端口的程序，或修改PORT变量")
        else:
            print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
