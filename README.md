# 🎬 Web TVBox - 网页版视频播放器

一个基于HTML/JavaScript的网页版TVBox播放器，支持解析TVBox数据源接口并在浏览器中播放视频。

## ✨ 功能特点

- 🌐 **纯网页版**: 无需安装任何应用，直接在浏览器中使用
- 📱 **响应式设计**: 支持电脑、平板、手机等各种设备
- 🔗 **数据源支持**: 兼容TVBox标准JSON数据源格式
- 🔍 **视频搜索**: 支持关键词搜索视频内容
- 🎥 **在线播放**: 直接在网页中播放视频
- 🚀 **CORS解决方案**: 内置多种方式解决跨域问题

## 📁 文件结构

```
web-tvbox/
├── index.html      # 主页面文件
├── app.js          # JavaScript核心功能
├── server.py       # 本地服务器（解决CORS问题）
└── README.md       # 说明文档
```

## 🚀 快速开始

### 方法一：直接打开（可能遇到CORS问题）

1. 直接用浏览器打开 `index.html` 文件
2. 在数据源配置区域选择预设源或输入自定义源
3. 点击"加载数据源"
4. 搜索并播放视频

### 方法二：使用本地服务器（推荐）

1. **启动本地服务器**：
   ```bash
   python3 server.py
   ```

2. **打开浏览器**：
   访问 `http://localhost:8000`

3. **配置数据源**：
   - 选择预设的数据源，或
   - 输入自定义数据源URL

4. **搜索视频**：
   在搜索框中输入关键词并点击搜索

5. **播放视频**：
   点击搜索结果中的视频卡片即可播放

## 📡 数据源配置

### 预设数据源

播放器内置了多个预设数据源：
- 小盒子单仓
- 肥猫接口
- 饭太硬
- 摸鱼接口
- OK线路
- 欧歌免费

### 自定义数据源

你也可以使用自己的TVBox数据源：
1. 在"自定义数据源URL"输入框中输入JSON接口地址
2. 点击"测试连接"验证数据源
3. 点击"加载数据源"开始使用

### 数据源格式

支持标准的TVBox JSON格式：
```json
{
  "sites": [
    {
      "key": "site_key",
      "name": "站点名称",
      "type": 3,
      "api": "站点API地址",
      "searchable": true
    }
  ],
  "spider": "爬虫地址",
  "lives": [...]
}
```

## 🔧 技术特点

### CORS解决方案

为了解决跨域请求问题，播放器提供了多种解决方案：

1. **本地代理**: 使用 `server.py` 提供的代理服务
2. **公共代理**: 自动尝试使用公共CORS代理服务
3. **直接请求**: 对于支持CORS的数据源直接请求

### 响应式设计

- 桌面端：多列网格布局，完整功能
- 移动端：单列布局，触摸友好
- 平板端：自适应布局

## 🛠️ 开发说明

### 本地开发

1. **克隆或下载项目文件**
2. **启动开发服务器**：
   ```bash
   python3 server.py
   ```
3. **在浏览器中打开** `http://localhost:8000`

### 自定义修改

- **样式修改**: 编辑 `index.html` 中的CSS部分
- **功能扩展**: 修改 `app.js` 中的JavaScript代码
- **服务器配置**: 修改 `server.py` 中的端口和代理设置

## ⚠️ 注意事项

1. **网络要求**: 需要稳定的网络连接访问数据源
2. **浏览器兼容**: 建议使用现代浏览器（Chrome、Firefox、Safari、Edge）
3. **CORS限制**: 某些数据源可能需要使用本地服务器解决跨域问题
4. **内容合规**: 请确保使用的数据源内容符合当地法律法规

## 🔍 故障排除

### 常见问题

**Q: 数据源加载失败？**
A: 
- 检查网络连接
- 尝试使用本地服务器 (`python3 server.py`)
- 更换其他数据源

**Q: 视频无法播放？**
A: 
- 确认视频源地址有效
- 检查浏览器是否支持该视频格式
- 尝试其他视频源

**Q: 搜索没有结果？**
A: 
- 确认数据源已正确加载
- 尝试不同的关键词
- 检查所选站点是否支持搜索

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

**免责声明**: 本项目仅为技术演示，不提供任何视频内容。所有视频内容来源于用户配置的第三方数据源，请用户自行确保内容的合法性。
