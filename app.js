// Web TVBox - 网页版视频播放器
// 全局变量
let currentDataSource = null;
let videoSites = [];
let searchResults = [];

// 工具函数
function showStatus(elementId, message, type = 'info') {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    element.className = type;
    element.innerHTML = message;
    
    // 自动清除成功消息
    if (type === 'success') {
        setTimeout(() => {
            element.innerHTML = '';
            element.className = '';
        }, 3000);
    }
}

function showLoading(elementId, message = '加载中...') {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    element.innerHTML = `<div class="loading">${message}</div>`;
}

// CORS代理函数
async function fetchWithCORS(url) {
    // 尝试多种方式获取数据
    const proxies = [
        '', // 直接请求
        '/proxy?url=', // 本地代理
        'https://cors-anywhere.herokuapp.com/', // 公共代理
        'https://api.allorigins.win/get?url=', // AllOrigins代理
    ];

    for (let proxy of proxies) {
        try {
            let proxyUrl;
            if (!proxy) {
                proxyUrl = url;
            } else if (proxy.includes('allorigins')) {
                proxyUrl = proxy + encodeURIComponent(url);
            } else {
                proxyUrl = proxy + encodeURIComponent(url);
            }

            console.log(`尝试请求: ${proxyUrl}`);
            const response = await fetch(proxyUrl);

            if (response.ok) {
                if (proxy.includes('allorigins')) {
                    const data = await response.json();
                    return { json: () => JSON.parse(data.contents) };
                }
                return response;
            }
        } catch (error) {
            console.log(`代理 ${proxy || '直接请求'} 失败:`, error);
            continue;
        }
    }

    throw new Error('所有请求方式都失败了，可能是CORS限制。请尝试使用本地服务器：python3 server.py');
}

// 加载预设数据源
function loadPresetSource(name, url) {
    document.getElementById('sourceUrl').value = url;
    showStatus('sourceStatus', `已选择: ${name}`, 'success');
    loadDataSource();
}

// 加载数据源
async function loadDataSource() {
    const url = document.getElementById('sourceUrl').value.trim();
    
    if (!url) {
        showStatus('sourceStatus', '请输入数据源URL', 'error');
        return;
    }
    
    showStatus('sourceStatus', '正在加载数据源...', 'loading');
    
    try {
        const response = await fetchWithCORS(url);
        const data = await response.json();
        
        currentDataSource = data;
        videoSites = data.sites || [];
        
        // 更新站点选择器
        updateSiteSelector();
        
        showStatus('sourceStatus', `数据源加载成功！找到 ${videoSites.length} 个视频站点`, 'success');
        
        console.log('数据源加载成功:', data);
        
    } catch (error) {
        console.error('加载数据源失败:', error);
        showStatus('sourceStatus', `加载失败: ${error.message}`, 'error');
        
        // 提供解决方案
        showStatus('sourceStatus', `
            <div class="error">
                <strong>加载失败: ${error.message}</strong><br>
                <small>解决方案：<br>
                1. 检查网络连接<br>
                2. 尝试其他数据源<br>
                3. 使用浏览器扩展解决CORS问题<br>
                4. 或者下载数据源文件到本地使用</small>
            </div>
        `, 'error');
    }
}

// 更新站点选择器
function updateSiteSelector() {
    const siteSelect = document.getElementById('siteSelect');
    siteSelect.innerHTML = '<option value="">所有站点</option>';
    
    videoSites.forEach(site => {
        if (site.searchable !== false) {
            const option = document.createElement('option');
            option.value = site.key;
            option.textContent = `${site.name} (${site.type})`;
            siteSelect.appendChild(option);
        }
    });
}

// 测试数据源连接
async function testSource() {
    const url = document.getElementById('sourceUrl').value.trim();
    
    if (!url) {
        showStatus('sourceStatus', '请先输入数据源URL', 'error');
        return;
    }
    
    showStatus('sourceStatus', '正在测试连接...', 'loading');
    
    try {
        const response = await fetchWithCORS(url);
        const data = await response.json();
        
        if (data && (data.sites || data.spider)) {
            showStatus('sourceStatus', '✅ 连接测试成功！数据源格式正确', 'success');
        } else {
            showStatus('sourceStatus', '⚠️ 连接成功但数据格式可能不正确', 'error');
        }
        
    } catch (error) {
        showStatus('sourceStatus', `❌ 连接测试失败: ${error.message}`, 'error');
    }
}

// 搜索视频
async function searchVideos() {
    const keyword = document.getElementById('searchKeyword').value.trim();
    const selectedSite = document.getElementById('siteSelect').value;
    
    if (!keyword) {
        showStatus('sourceStatus', '请输入搜索关键词', 'error');
        return;
    }
    
    if (!videoSites.length) {
        showStatus('sourceStatus', '请先加载数据源', 'error');
        return;
    }
    
    showLoading('searchResults', '正在搜索视频...');
    
    try {
        // 模拟搜索结果（实际需要调用各站点API）
        const mockResults = generateMockResults(keyword);
        displaySearchResults(mockResults);
        
    } catch (error) {
        console.error('搜索失败:', error);
        document.getElementById('searchResults').innerHTML = `
            <div class="error">搜索失败: ${error.message}</div>
        `;
    }
}

// 生成模拟搜索结果
function generateMockResults(keyword) {
    const mockData = [
        { name: `${keyword} 第一季`, type: '电视剧', year: '2023', desc: '精彩剧情，值得观看' },
        { name: `${keyword} 电影版`, type: '电影', year: '2024', desc: '高清画质，震撼视听' },
        { name: `${keyword} 纪录片`, type: '纪录片', year: '2023', desc: '真实记录，深度解析' },
        { name: `${keyword} 动画版`, type: '动画', year: '2024', desc: '精美画面，适合全家观看' }
    ];
    
    return mockData.map((item, index) => ({
        ...item,
        id: `mock_${index}`,
        playUrl: `https://example.com/play/${index}`, // 模拟播放地址
        poster: `https://via.placeholder.com/300x400/667eea/white?text=${encodeURIComponent(item.name)}`
    }));
}

// 显示搜索结果
function displaySearchResults(results) {
    const container = document.getElementById('searchResults');
    
    if (!results || results.length === 0) {
        container.innerHTML = '<div class="loading">没有找到相关视频</div>';
        return;
    }
    
    const html = `
        <div class="video-grid">
            ${results.map(video => `
                <div class="video-card" onclick="playVideo('${video.playUrl}', '${video.name}')">
                    <h3>${video.name}</h3>
                    <p><strong>类型:</strong> ${video.type}</p>
                    <p><strong>年份:</strong> ${video.year}</p>
                    <p><strong>简介:</strong> ${video.desc}</p>
                </div>
            `).join('')}
        </div>
    `;
    
    container.innerHTML = html;
    searchResults = results;
}

// 播放视频
function playVideo(videoUrl, title) {
    const playerSection = document.getElementById('playerSection');
    const videoPlayer = document.getElementById('videoPlayer');
    const videoInfo = document.getElementById('videoInfo');
    
    // 显示播放器
    playerSection.style.display = 'block';
    
    // 设置视频信息
    videoInfo.innerHTML = `
        <h3>正在播放: ${title}</h3>
        <p>视频地址: ${videoUrl}</p>
        <p style="color: #666; font-size: 0.9em;">
            注意: 这是演示版本，实际播放需要有效的视频源地址
        </p>
    `;
    
    // 尝试播放视频
    if (videoUrl.includes('example.com')) {
        // 模拟地址，显示提示
        videoPlayer.style.display = 'none';
        videoInfo.innerHTML += `
            <div class="error">
                这是模拟数据，无法播放。<br>
                实际使用时需要配置真实的数据源接口。
            </div>
        `;
    } else {
        videoPlayer.style.display = 'block';
        videoPlayer.src = videoUrl;
    }
    
    // 滚动到播放器
    playerSection.scrollIntoView({ behavior: 'smooth' });
}

// 处理搜索框回车事件
function handleSearchKeyPress(event) {
    if (event.key === 'Enter') {
        searchVideos();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('Web TVBox 播放器已加载');
    
    // 检查是否有保存的数据源
    const savedSource = localStorage.getItem('tvbox_source');
    if (savedSource) {
        document.getElementById('sourceUrl').value = savedSource;
    }
    
    // 保存数据源到本地存储
    document.getElementById('sourceUrl').addEventListener('change', function() {
        localStorage.setItem('tvbox_source', this.value);
    });
});

// 导出函数供HTML调用
window.loadPresetSource = loadPresetSource;
window.loadDataSource = loadDataSource;
window.testSource = testSource;
window.searchVideos = searchVideos;
window.playVideo = playVideo;
window.handleSearchKeyPress = handleSearchKeyPress;
