// Web TVBox - 网页版视频播放器
// 全局变量
let currentDataSource = null;
let videoSites = [];
let searchResults = [];

// 工具函数
function showStatus(elementId, message, type = 'info') {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    element.className = type;
    element.innerHTML = message;
    
    // 自动清除成功消息
    if (type === 'success') {
        setTimeout(() => {
            element.innerHTML = '';
            element.className = '';
        }, 3000);
    }
}

function showLoading(elementId, message = '加载中...') {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    element.innerHTML = `<div class="loading">${message}</div>`;
}

// CORS代理函数
async function fetchWithCORS(url) {
    // 尝试多种方式获取数据
    const proxies = [
        '', // 直接请求
        '/proxy?url=', // 本地代理
        'https://cors-anywhere.herokuapp.com/', // 公共代理
        'https://api.allorigins.win/get?url=', // AllOrigins代理
    ];

    for (let proxy of proxies) {
        try {
            let proxyUrl;
            if (!proxy) {
                proxyUrl = url;
            } else if (proxy.includes('allorigins')) {
                proxyUrl = proxy + encodeURIComponent(url);
            } else {
                proxyUrl = proxy + encodeURIComponent(url);
            }

            console.log(`尝试请求: ${proxyUrl}`);
            const response = await fetch(proxyUrl);

            if (response.ok) {
                if (proxy.includes('allorigins')) {
                    const data = await response.json();
                    return { json: () => JSON.parse(data.contents) };
                }
                return response;
            }
        } catch (error) {
            console.log(`代理 ${proxy || '直接请求'} 失败:`, error);
            continue;
        }
    }

    throw new Error('所有请求方式都失败了，可能是CORS限制。请尝试使用本地服务器：python3 server.py');
}

// 加载预设数据源
function loadPresetSource(name, url) {
    document.getElementById('sourceUrl').value = url;
    showStatus('sourceStatus', `已选择: ${name}`, 'success');
    loadDataSource();
}

// 显示手动输入
function showManualInput() {
    const url = prompt('请输入数据源URL:', 'https://');
    if (url && url.trim()) {
        document.getElementById('sourceUrl').value = url.trim();
        loadDataSource();
    }
}

// 加载数据源
async function loadDataSource() {
    const url = document.getElementById('sourceUrl').value.trim();
    
    if (!url) {
        showStatus('sourceStatus', '请输入数据源URL', 'error');
        return;
    }
    
    showStatus('sourceStatus', '正在加载数据源...', 'loading');
    
    try {
        const response = await fetchWithCORS(url);
        const data = await response.json();
        
        currentDataSource = data;
        videoSites = data.sites || [];

        // 更新站点选择器
        updateSiteSelector();

        showStatus('sourceStatus', `数据源加载成功！找到 ${videoSites.length} 个视频站点`, 'success');

        // 输出前几个站点的信息用于调试
        console.log('数据源加载成功:', data);
        console.log('前5个站点信息:', videoSites.slice(0, 5));

        // 显示可搜索的站点
        displaySearchableSites();
        
    } catch (error) {
        console.error('加载数据源失败:', error);

        let errorMessage = error.message;
        let solutions = [];

        if (errorMessage.includes('CORS')) {
            solutions = [
                '1. 确保本地服务器正在运行 (python3 server.py)',
                '2. 尝试使用本地测试源',
                '3. 检查数据源URL是否正确'
            ];
        } else if (errorMessage.includes('SSL') || errorMessage.includes('certificate')) {
            solutions = [
                '1. 数据源SSL证书问题，已自动处理',
                '2. 尝试使用HTTP协议的数据源',
                '3. 检查网络连接'
            ];
        } else {
            solutions = [
                '1. 检查网络连接',
                '2. 验证数据源URL格式',
                '3. 尝试其他数据源'
            ];
        }

        const solutionText = solutions.join('<br>');
        showStatus('sourceStatus', `
            <div class="error">
                <strong>加载失败: ${errorMessage}</strong><br>
                <small>解决方案：<br>${solutionText}</small>
            </div>
        `, 'error');
    }
}

// 更新站点选择器
function updateSiteSelector() {
    const siteSelect = document.getElementById('siteSelect');
    if (!siteSelect) return;

    siteSelect.innerHTML = '<option value="">选择站点</option>';

    videoSites.forEach(site => {
        if (site.searchable !== false) {
            const option = document.createElement('option');
            option.value = site.key;
            option.textContent = `${site.name} (类型${site.type})`;
            siteSelect.appendChild(option);
        }
    });
}

// 显示可搜索的站点列表
function displaySearchableSites() {
    const httpApiSites = videoSites.filter(site =>
        site.api &&
        site.api.startsWith('http') &&
        !site.api.includes('csp_')
    );

    const cspSites = videoSites.filter(site => site.api && (site.api.startsWith('csp_') || site.api.includes('csp_')));
    const noApiSites = videoSites.filter(site => !site.api);

    const container = document.getElementById('searchResults');

    if (!container) return;

    const html = `
        <h3>HTTP API站点 (${httpApiSites.length}/${videoSites.length})</h3>
        ${httpApiSites.length > 0 ? `
        <div class="video-grid">
            ${httpApiSites.map(site => `
                <div class="video-card" onclick="selectSiteForSearch('${site.key}')">
                    <div class="video-card-content">
                        <h3>${site.name}</h3>
                        <p><strong>类型:</strong> ${site.type}</p>
                        <p><strong>API:</strong> ${site.api.length > 50 ? site.api.substring(0, 50) + '...' : site.api}</p>
                        <p><strong>状态:</strong> <span style="color: orange;">🧪 待测试</span></p>
                        <button class="btn" onclick="event.stopPropagation(); testSiteApi('${site.key}')" style="margin-top: 10px;">测试连接</button>
                    </div>
                </div>
            `).join('')}
        </div>
        ` : '<p>没有找到HTTP API站点</p>'}

        <details style="margin-top: 20px;">
            <summary><strong>其他站点统计</strong></summary>
            <p>🔒 CSP协议站点: ${cspSites.length} 个（需要TVBox应用）</p>
            <p>❌ 无API站点: ${noApiSites.length} 个</p>
            <p>💡 只有HTTP API站点可以在网页版中测试使用</p>
        </details>
    `;

    container.innerHTML = html;
}

// 测试数据源连接
async function testSource() {
    const url = document.getElementById('sourceUrl').value.trim();

    if (!url) {
        showStatus('sourceStatus', '请先输入数据源URL', 'error');
        return;
    }

    showStatus('sourceStatus', '正在测试连接...', 'loading');

    try {
        const response = await fetchWithCORS(url);
        const data = await response.json();

        if (data && (data.sites || data.spider)) {
            showStatus('sourceStatus', '✅ 连接测试成功！数据源格式正确', 'success');
        } else {
            showStatus('sourceStatus', '⚠️ 连接成功但数据格式可能不正确', 'error');
        }

    } catch (error) {
        showStatus('sourceStatus', `❌ 连接测试失败: ${error.message}`, 'error');
    }
}

// 测试单个站点API
async function testSiteApi(siteKey) {
    const site = videoSites.find(s => s.key === siteKey);
    if (!site) return;

    const testKeyword = '测试';

    try {
        // 构造测试URL
        let testUrl;
        if (site.type === 3) {
            testUrl = `${site.api}?ac=detail&wd=${encodeURIComponent(testKeyword)}`;
        } else if (site.type === 1) {
            testUrl = `${site.api}?wd=${encodeURIComponent(testKeyword)}`;
        } else {
            testUrl = `${site.api}?ac=list`;
        }

        console.log(`测试站点 ${site.name}: ${testUrl}`);

        // 更新按钮状态
        const button = event.target;
        const originalText = button.textContent;
        button.textContent = '测试中...';
        button.disabled = true;

        const response = await fetchWithCORS(testUrl);
        const data = await response.json();

        console.log(`${site.name} 测试结果:`, data);

        // 检查响应格式
        if (data && (data.list || data.data || data.code === 1)) {
            button.textContent = '✅ 可用';
            button.style.backgroundColor = '#48bb78';

            // 更新站点状态显示
            const statusElement = button.parentElement.querySelector('p:last-child');
            if (statusElement) {
                statusElement.innerHTML = '<strong>状态:</strong> <span style="color: green;">✅ 测试通过</span>';
            }
        } else {
            button.textContent = '❌ 异常';
            button.style.backgroundColor = '#f56565';
        }

    } catch (error) {
        console.error(`测试站点 ${site.name} 失败:`, error);

        const button = event.target;
        button.textContent = '❌ 失败';
        button.style.backgroundColor = '#f56565';

        // 显示错误信息
        const statusElement = button.parentElement.querySelector('p:last-child');
        if (statusElement) {
            statusElement.innerHTML = `<strong>状态:</strong> <span style="color: red;">❌ ${error.message}</span>`;
        }
    } finally {
        const button = event.target;
        button.disabled = false;
    }
}

// 选择站点进行搜索
function selectSiteForSearch(siteKey) {
    const site = videoSites.find(s => s.key === siteKey);
    if (!site) return;

    const siteSelect = document.getElementById('siteSelect');
    if (siteSelect) {
        siteSelect.value = siteKey;
    }

    showStatus('sourceStatus', `已选择站点: ${site.name}`, 'success');

    // 显示站点详细信息
    const container = document.getElementById('searchResults');
    container.innerHTML = `
        <div class="video-card" style="max-width: 600px; margin: 0 auto;">
            <div class="video-card-content">
                <h3>${site.name}</h3>
                <p><strong>站点Key:</strong> ${site.key}</p>
                <p><strong>类型:</strong> ${site.type}</p>
                <p><strong>API地址:</strong> ${site.api || '未提供'}</p>
                <p><strong>可搜索:</strong> ${site.searchable !== false ? '是' : '否'}</p>
                <p><strong>快速搜索:</strong> ${site.quickSearch ? '是' : '否'}</p>
                <p><strong>可筛选:</strong> ${site.filterable ? '是' : '否'}</p>
                ${site.ext ? `<p><strong>扩展信息:</strong> ${site.ext}</p>` : ''}
                <br>
                <button class="btn" onclick="searchInSite('${siteKey}')">在此站点搜索</button>
            </div>
        </div>
    `;
}

// 在指定站点搜索
async function searchInSite(siteKey) {
    const keyword = document.getElementById('searchKeyword').value.trim();
    const site = videoSites.find(s => s.key === siteKey);

    if (!keyword) {
        showStatus('sourceStatus', '请输入搜索关键词', 'error');
        return;
    }

    if (!site) {
        showStatus('sourceStatus', '站点不存在', 'error');
        return;
    }

    showLoading('searchResults', `正在 ${site.name} 中搜索 "${keyword}"...`);

    try {
        // 尝试调用站点API进行搜索
        await performSiteSearch(site, keyword);

    } catch (error) {
        console.error('搜索失败:', error);
        document.getElementById('searchResults').innerHTML = `
            <div class="error">
                在 ${site.name} 搜索失败: ${error.message}<br>
                <small>可能原因：站点API格式未知、需要特殊参数、或站点不可访问</small>
            </div>
        `;
    }
}

// 执行站点搜索
async function performSiteSearch(site, keyword) {
    // 检查API格式
    if (!site.api) {
        throw new Error('站点没有提供API地址');
    }

    // 检查是否是CSP协议（TVBox自定义协议）
    if (site.api.startsWith('csp_')) {
        throw new Error(`站点使用CSP协议 (${site.api})，需要TVBox应用才能调用，网页版无法直接使用`);
    }

    // 检查是否是相对路径
    if (!site.api.startsWith('http')) {
        throw new Error(`站点API不是完整URL: ${site.api}`);
    }

    // 根据站点类型构造搜索URL
    let searchUrl;

    if (site.type === 3) {
        // 类型3通常是CMS系统
        searchUrl = `${site.api}?ac=detail&wd=${encodeURIComponent(keyword)}`;
    } else if (site.type === 1) {
        // 类型1通常是其他格式
        searchUrl = `${site.api}?wd=${encodeURIComponent(keyword)}`;
    } else if (site.type === 0) {
        // 类型0可能是直播源，不支持搜索
        throw new Error('此站点是直播源，不支持视频搜索');
    } else {
        throw new Error(`不支持的站点类型: ${site.type}`);
    }

    console.log(`搜索URL: ${searchUrl}`);

    // 通过代理请求搜索
    const response = await fetchWithCORS(searchUrl);
    const data = await response.json();

    console.log('搜索结果:', data);

    // 解析搜索结果
    displayRealSearchResults(data, site);
}

// 显示真实搜索结果
function displayRealSearchResults(data, site) {
    const container = document.getElementById('searchResults');

    if (!data || !data.list || data.list.length === 0) {
        container.innerHTML = `
            <div class="loading">
                在 ${site.name} 中没有找到相关内容<br>
                <small>返回数据: ${JSON.stringify(data).substring(0, 200)}...</small>
            </div>
        `;
        return;
    }

    const videos = data.list;
    const html = `
        <h3>在 ${site.name} 找到 ${videos.length} 个结果</h3>
        <div class="video-grid">
            ${videos.map(video => `
                <div class="video-card" onclick="playRealVideo('${video.vod_play_url || video.vod_play_from}', '${video.vod_name}')">
                    <div class="video-card-content">
                        <h3>${video.vod_name}</h3>
                        <p><strong>类型:</strong> ${video.type_name || '未知'}</p>
                        <p><strong>年份:</strong> ${video.vod_year || '未知'}</p>
                        <p><strong>地区:</strong> ${video.vod_area || '未知'}</p>
                        <p><strong>简介:</strong> ${(video.vod_content || video.vod_blurb || '无简介').substring(0, 100)}...</p>
                    </div>
                </div>
            `).join('')}
        </div>
    `;

    container.innerHTML = html;
}

// 搜索视频（保持原有接口）
async function searchVideos() {
    const keyword = document.getElementById('searchKeyword').value.trim();
    const selectedSite = document.getElementById('siteSelect').value;

    if (!keyword) {
        showStatus('sourceStatus', '请输入搜索关键词', 'error');
        return;
    }

    if (!videoSites.length) {
        showStatus('sourceStatus', '请先加载数据源', 'error');
        return;
    }

    if (selectedSite) {
        // 在指定站点搜索
        await searchInSite(selectedSite);
    } else {
        // 显示可搜索站点列表
        displaySearchableSites();
        showStatus('sourceStatus', '请先选择一个站点进行搜索', 'error');
    }
}

// 删除模拟搜索结果函数

// 显示搜索结果
function displaySearchResults(results) {
    const container = document.getElementById('searchResults');
    
    if (!results || results.length === 0) {
        container.innerHTML = '<div class="loading">没有找到相关视频</div>';
        return;
    }
    
    const html = `
        <div class="video-grid">
            ${results.map(video => `
                <div class="video-card" onclick="playVideo('${video.playUrl}', '${video.name}')">
                    <h3>${video.name}</h3>
                    <p><strong>类型:</strong> ${video.type}</p>
                    <p><strong>年份:</strong> ${video.year}</p>
                    <p><strong>简介:</strong> ${video.desc}</p>
                </div>
            `).join('')}
        </div>
    `;
    
    container.innerHTML = html;
    searchResults = results;
}

// 播放真实视频
function playRealVideo(playInfo, title) {
    const playerSection = document.getElementById('playerSection');
    const videoPlayer = document.getElementById('videoPlayer');
    const videoInfo = document.getElementById('videoInfo');

    // 显示播放器
    playerSection.style.display = 'block';

    // 解析播放信息
    let videoUrl = '';
    if (typeof playInfo === 'string') {
        // 如果是字符串，可能包含多个播放源
        const lines = playInfo.split('\n').filter(line => line.trim());
        if (lines.length > 0) {
            // 取第一个播放源
            const firstLine = lines[0];
            if (firstLine.includes('$')) {
                videoUrl = firstLine.split('$')[1];
            } else {
                videoUrl = firstLine;
            }
        }
    } else {
        videoUrl = playInfo;
    }

    // 设置视频信息
    videoInfo.innerHTML = `
        <h3>正在播放: ${title}</h3>
        <p>视频地址: ${videoUrl}</p>
        <p><strong>原始播放信息:</strong></p>
        <pre style="background: #f5f5f5; padding: 10px; border-radius: 5px; font-size: 12px; max-height: 200px; overflow-y: auto;">${playInfo}</pre>
    `;

    // 尝试播放视频
    if (videoUrl && (videoUrl.startsWith('http') || videoUrl.startsWith('//') || videoUrl.endsWith('.m3u8') || videoUrl.endsWith('.mp4'))) {
        videoPlayer.style.display = 'block';
        videoPlayer.src = videoUrl;
    } else {
        videoPlayer.style.display = 'none';
        videoInfo.innerHTML += `
            <div class="error">
                无法解析播放地址。可能需要进一步处理播放信息。
            </div>
        `;
    }

    // 滚动到播放器
    playerSection.scrollIntoView({ behavior: 'smooth' });
}

// 播放视频（保持原有接口）
function playVideo(videoUrl, title) {
    playRealVideo(videoUrl, title);
}

// 处理搜索框回车事件
function handleSearchKeyPress(event) {
    if (event.key === 'Enter') {
        searchVideos();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('Web TVBox 播放器已加载');
    
    // 检查是否有保存的数据源
    const savedSource = localStorage.getItem('tvbox_source');
    if (savedSource) {
        document.getElementById('sourceUrl').value = savedSource;
    }
    
    // 保存数据源到本地存储
    document.getElementById('sourceUrl').addEventListener('change', function() {
        localStorage.setItem('tvbox_source', this.value);
    });
});

// 导出函数供HTML调用
window.loadPresetSource = loadPresetSource;
window.loadDataSource = loadDataSource;
window.testSource = testSource;
window.testSiteApi = testSiteApi;
window.searchVideos = searchVideos;
window.playVideo = playVideo;
window.playRealVideo = playRealVideo;
window.selectSiteForSearch = selectSiteForSearch;
window.searchInSite = searchInSite;
window.showManualInput = showManualInput;
window.handleSearchKeyPress = handleSearchKeyPress;
